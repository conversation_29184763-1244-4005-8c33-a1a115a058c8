{"name": "paint-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"clsx": "^2.1.1", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0", "sass": "^1.90.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "typescript": "^5"}}