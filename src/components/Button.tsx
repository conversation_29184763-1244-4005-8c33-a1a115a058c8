import React from "react";
import ArrowRight from "@/icons/ArrowRightBig.svg";
import { ClassValue } from "clsx";
import { cn } from "@/utils/class-util";

function Button({
  children,
  className,
  variant = "yellow",
}: {
  children: React.ReactNode;
  className?: ClassValue;
  variant?: "yellow" | "white" | "black";
}) {
  return (
    <button
      className={cn(
        "px-12.5 rounded-full flex items-center gap-18 bg-[#F9B800] relative duration-300 h-19.5 max-h-min bounce-rotate-animation border-transparent border-2 hover:border-black hover:before:bg-black button-animation",
        variant === "white" &&
          "bg-white hover:border-[#F9B800] hover:before:bg-[#F9B800]",
        variant === "black" &&
          "bg-black hover:border-white hover:before:bg-white",
        className
      )}
    >
      <span className="font-bold text-2xl">{children}</span>
      <ArrowRight className="bouncing-arrow fill-black" />
    </button>
  );
}

export default Button;
