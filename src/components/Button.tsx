import React from "react";
import ArrowRight from "@/icons/ArrowRightBig.svg";
import { ClassValue } from "clsx";
import { cn } from "@/utils/class-util";

function Button({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: ClassValue;
}) {
  return (
    <button
      className={cn(
        "px-12.5 rounded-full flex items-center gap-18 bg-[#F9B800] h-19.5 max-h-min bounce-rotate-animation",
        className
      )}
    >
      <span className="font-bold text-2xl">{children}</span>
      <ArrowRight className="bouncing-arrow" />
    </button>
  );
}

export default Button;
