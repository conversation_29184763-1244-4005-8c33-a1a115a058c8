@import "tailwindcss";

:root {
  --background: #FEEDBC;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #FEEDBC;
    --foreground: #ededed;
  }
} */

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* --- KEYFRAMES --- */

/* Your original keyframes for the hover-in effect */
@keyframes rotate-and-bounce-down {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(115deg);
  }

  70% {
    transform: rotate(80deg);
  }

  90% {
    transform: rotate(95deg);
  }

  100% {
    transform: rotate(90deg);
  }
}

/* New keyframes for the hover-out effect */
@keyframes rotate-and-bounce-up {
  0% {
    transform: rotate(90deg);
  }

  50% {
    transform: rotate(-20deg);
  }

  70% {
    transform: rotate(10deg);
  }

  90% {
    transform: rotate(-5deg);
  }

  100% {
    transform: rotate(0deg);
  }
}


/* --- ANIMATION RULES --- */

.bouncing-arrow {
  /* This is the default (hover-out) animation */
  animation: rotate-and-bounce-up 0.4s forwards ease-in;
}

.bounce-rotate-animation:hover .bouncing-arrow {
  /* This animation overrides the default on hover */
  animation: rotate-and-bounce-down 0.4s forwards ease-out;
}




.card-background-animation {
  position: relative;
  overflow: clip;
  cursor: pointer;
}

.card-background-animation:hover {
  color: white;
}

.card-content {
  position: relative;
  z-index: 2;
  padding: 2rem;
  text-align: center;
}

.card-background-animation::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: #262626;
  border-radius: 100%;
  z-index: 1;
  left: 50%;
  top: 100%;
  transform: translateY(50%) translateX(-50%) scale(1);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  padding: 10px;
  /* Default state - positioned down */
  transition: none;
}


@keyframes background-double-bounce-up {
  0% {
    transform: translateY(0%) translateX(-50%) scale(1);
  }

  35% {
    transform: translateY(-105%) translateX(-50%) scale(1.1);
  }

  54% {
    transform: translateY(-60%) translateX(-50%) scale(1.2);
  }

  80% {
    transform: translateY(-102%) translateX(-50%) scale(1.3);
  }

  90% {
    transform: translateY(-94%) translateX(-50%) scale(1.3);
  }

  100% {
    transform: translateY(-100%) translateX(-50%) scale(1.3);
  }
}

@keyframes background-double-bounce-down {
  0% {
    transform: translateY(-100%) translateX(-50%) scale(1.3);
  }



  50% {
    transform: translateY(-10%) translateX(-50%) scale(1);
  }

  65% {
    transform: translateY(-25%) translateX(-50%) scale(1);
  }

  85% {
    transform: translateY(0%) translateX(-50%) scale(1);
  }

  95% {
    transform: translateY(-10%) translateX(-50%) scale(1);
  }

  100% {
    transform: translateY(0%) translateX(-50%) scale(1);
  }
}



.card-background-animation:hover::before {
  animation: background-double-bounce-up 0.6s forwards cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Trigger down animation when hover ends */
.card-background-animation:not(:hover)::before {
  animation: background-double-bounce-down 0.6s forwards cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.button-animation::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 999;
  z-index: -1;
  transition: all 0.3s ease;
  inset: 0;
  opacity: 0;
}

.button-animation:hover::before {
  transform: translateY(14%) translateX(2%);
  opacity: 1;
}