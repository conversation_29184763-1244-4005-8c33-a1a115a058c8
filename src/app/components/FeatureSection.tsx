import Button from "@/components/Button";
import Image from "next/image";
import React from "react";
import JoinImage from "@/assets/images/join.png";
import HandImage from "@/assets/images/two-hand.png";
import UnityImage from "@/assets/images/unity-hand.png";
import MeditationImage from "@/assets/images/meditation.png";
import FeatureCard from "./FeatureCard";

function FeatureSection() {
  return (
    <section className="relative overflow-x-clip">
      <div
        className="bg-[#262626] absolute w-[170%] h-[140%] -z-10"
        style={{
          // transformOrigin: "50% 50%",
          top: -90,
          // left: "50%",
          transform: " skew(0,-12deg)",
        }}
      ></div>
      <div className="flex items-center container mx-auto  pt-42 mt-64 justify-between">
        <div className="flex-1 max-w-xl">
          <p className="text-xl text-[#F9B800]">
            Quisque porttitor vitae vel amet neque scelerisque mattis.
            Consectetur nibh velit magna consectetur leo.
          </p>
          <h3 className="font-bold text-[50px] text-white mt-3.5">
            Cursus Integer Conseq Aliquam Tristique.
          </h3>
          <Button variant="white" className="mt-15">
            Lorem Ipsum
          </Button>
        </div>
        <div className="flex-1 flex  items-start gap-15 justify-end">
          <div className="grid gap-15 ">
            <FeatureCard
              image={JoinImage}
              index={1}
              title="Phasellus Vitae"
              subtitle="Quisque"
              description="Porttitor vitae vel amet"
              className="shadow-xl shadow-black"
            />
            <FeatureCard
              image={UnityImage}
              index={3}
              title="Eleifend Pulvinar "
              subtitle="Vitae"
              description="Consectetur nibh velit"
              yellow
              className=""
            />
          </div>
          <div className="grid gap-15 mt-32">
            <FeatureCard
              image={HandImage}
              index={2}
              title="Iaculis Magna"
              subtitle="Porttitor"
              description="neque scelerisque mattis."
              yellow
            />
            <FeatureCard
              image={MeditationImage}
              index={4}
              title="Velit Odio Phir"
              subtitle="Ametneq"
              description="magna consectetur leo."
              className=""
            />
          </div>
        </div>
      </div>
    </section>
  );
}

export default FeatureSection;
