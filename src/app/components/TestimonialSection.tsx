import React from "react";
import UserImage from "@/assets/images/user.png";
import Image from "next/image";
import ArrowLeft from "@/assets/icons/ArrowLeftLean.svg";
import ArrowRight from "@/assets/icons/ArrowRightLean.svg";

function TestimonialSection() {
  return (
    <div className="bg-white rounded-[100px] container testimonial-container group hover:text-white px-34 py-25 mx-auto max-w-[1650px] relative flex items-center gap-17.5 overflow-clip">
      <div className="testimonial-overlay absolute inset-0 w-full h-full bg-[#262626c9] rounded-[100px]">
        <Image
          src={UserImage}
          alt="User Image"
          className="w-full h-full object-cover"
        />

        <div className="absolute inset-0 w-full h-full bg-[#262626c9] rounded-[100px]"></div>
      </div>
      <div className="relative">
        <Image
          src={UserImage}
          alt="User Image"
          width={500}
          height={500}
          className="w-119 h-119 rounded-full min-w-max"
        />
      </div>

      <div className="relative">
        <h2 className="text-5xl font-bold">What our customers thought?</h2>
        <p className="mt-10 text-3xl text-[#262626] group-hover:text-white ">
          Euismod magna id purus eget nunc ligula suspendisse dui netus.
          Condimentum blandit rutrum at mauris enim pulvinar duis etiam duis.
        </p>
        <h4 className="mt-5 text-2xl font-bold group-hover:text-[#F9B800]">
          Holly Davidson
        </h4>

        <div className="flex gap-10 mt-10">
          <button>
            <ArrowLeft className="fill-[#262626] group-hover:fill-white" />
          </button>
          <button>
            <ArrowRight />
          </button>
        </div>
      </div>
    </div>
  );
}

export default TestimonialSection;
