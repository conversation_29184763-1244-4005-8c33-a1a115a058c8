import Badge from "@/components/Badge";
import Button from "@/components/Button";
import Image from "next/image";
import React from "react";
import illustration from "@/assets/images/illustration-1.png";

function HeroSection() {
  return (
    <section className="container mx-auto flex mt-29">
      <div className="flex-1">
        <div className="">
          <h3 className="text-3xl uppercase font-bold text-[#F9B800]">
            risus praesent vulputate.
          </h3>
          <h1 className="text-[80px] font-bold min-w-max leading-22">
            <span>Cursus Integer</span> <br />
            <span>Consequat Tristique.</span>
          </h1>
        </div>
        <div className="flex flex-wrap gap-3.5 mt-15">
          <Badge>Cursus Integer</Badge>
          <Badge>Integer Consequat</Badge>
          <Badge>Tellus Euismod Pellentesque</Badge>
          <Badge>Aliquot Tristique</Badge>
          <Badge>Pellentesque Tempus</Badge>
          <Badge>Mauris Fermentum Praesent</Badge>
        </div>
        <Button className="mt-15">Lorem Ipsum</Button>
      </div>
      <div className="flex-1">
        <Image
          src={illustration}
          alt="hero"
          width={800}
          height={800}
          className="w-full"
        />
      </div>
    </section>
  );
}

export default HeroSection;
