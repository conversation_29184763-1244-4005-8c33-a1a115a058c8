import HeroSection from "./components/HeroSection";
import Header from "./components/Header";
import FeatureSection from "./components/FeatureSection";
import TestimonialSection from "./components/TestimonialSection";
import InfoSection from "./components/InfoSection";
import FooterSection from "./components/FooterSection";

export default function Home() {
  return (
    <main className="overflow-clip">
      <Header />
      <HeroSection />
      <FeatureSection />
      <div className="relative min-h-screen mt-50">
        <div
          className="bg-[#F9B800] absolute w-[170%] h-full -z-20"
          style={{
            transformOrigin: "50% 50%",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%) skew(0,-12deg)",
          }}
        ></div>
        <TestimonialSection />
        <InfoSection />
      </div>
      <FooterSection />
    </main>
  );
}
